# 调试修复记录

## getCurrentUser 获取不到当前登录用户信息问题修复

### 问题描述

App 中的 getCurrentUser 方法获取不到当前登录用户的信息。

### 问题分析

通过代码分析发现以下问题：

1. **登录时未保存用户信息**：在 `pages/login/login.vue` 中，登录成功后只保存了 token，没有保存从服务器返回的用户信息。

2. **App 启动时未初始化状态**：`App.vue` 中没有调用 Vuex store 的 `initApp` 方法来从本地存储恢复用户信息。

3. **数据格式处理不一致**：不同地方对用户信息的存储和读取格式处理不一致，有些地方假设是字符串，有些地方假设是对象。

4. **缺少容错机制**：当本地存储中有 token 但没有用户信息时，没有主动获取用户信息的机制。

### 修复方案

#### 1. 修复登录流程 (`pages/login/login.vue`)

```javascript
// 保存token和用户信息
uni.setStorageSync("token", res.token);
if (res.user) {
  uni.setStorageSync("userInfo", res.user);
  // 同时更新 Vuex store
  this.$store.dispatch("login", res.user);
}
```

#### 2. 修复应用启动初始化 (`App.vue`)

```javascript
onLaunch: function () {
  console.log('App Launch')
  // 初始化应用状态，从本地存储恢复用户信息
  this.$store.dispatch('initApp')
}
```

#### 3. 统一数据格式处理

- 修复 `utils/auth.js` 中的 `getUserInfo` 方法，支持对象和字符串两种格式
- 修复 Vuex store 中的 `initApp` 方法，正确解析用户信息
- 修复 `pages/task/detail.vue` 中的 `getCurrentUser` 方法，增加详细日志和容错处理

#### 4. 增加自动获取用户信息机制

在 Vuex store 的 `initApp` 方法中：

```javascript
else if (token) {
  // 如果有 token 但没有用户信息，尝试获取用户信息
  try {
    const { getUserInfo } = await import("@/api/auth");
    const res = await getUserInfo();
    if (res && res.data) {
      commit("SET_USER_INFO", res.data);
      uni.setStorageSync("userInfo", res.data);
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    // 如果获取失败，可能是 token 过期，清除本地存储
    uni.removeStorageSync("token");
    uni.removeStorageSync("userInfo");
  }
}
```

#### 5. 添加 Vuex getter

```javascript
getters: {
  // 用户信息
  userInfo: (state) => state.userInfo,
  // 用户ID
  userId: (state) => state.userInfo.userId,
  // 其他 getters...
}
```

### 修复后的使用方式

#### 在组件中获取用户信息的推荐方式：

```javascript
// 方式1：使用 Vuex getter（推荐）
const userInfo = this.$store.getters.userInfo;
const userId = this.$store.getters.userId;

// 方式2：使用工具函数
import { getCurrentUser } from "@/utils/auth";
const userInfo = getCurrentUser();

// 方式3：直接从本地存储获取（不推荐）
const userInfo = uni.getStorageSync("userInfo");
```

### 测试验证

修复完成后，可以通过以下方式验证：

1. **登录测试**：登录后检查本地存储和 Vuex store 中是否都有用户信息
2. **重启测试**：重启应用后检查是否能正确恢复用户信息
3. **页面测试**：在各个页面中调用 `getCurrentUser` 检查是否能获取到用户信息

### 注意事项

1. 确保服务器端登录 API 返回完整的用户信息
2. 用户信息的存储格式要保持一致
3. 在获取用户信息失败时要有适当的错误处理
4. 考虑 token 过期的情况，及时清理本地存储

### 修复文件列表

1. `pages/login/login.vue` - 修复登录时保存用户信息
2. `App.vue` - 添加应用启动时的状态初始化
3. `store/index.js` - 修复 initApp 方法，添加 userInfo getter
4. `utils/auth.js` - 修复 getUserInfo 方法，添加 getCurrentUser 方法
5. `pages/task/detail.vue` - 修复 getCurrentUser 方法，增加日志和容错

---

# 地址选择和任务类型回显问题修复

## 问题描述

1. **地址选择后页面不回显**: 选择地址后，输入框中不显示选择的地址
2. **任务类型没办法选择二级分类**: 选择一级类型后，二级类型无法选择
3. **任务类型不会回显到表单上**: 选择的任务类型不会显示在表单中

## 问题分析

### 1. 地址选择回显问题

- AddressSelector 组件的 v-model 双向绑定有问题
- selectedAddress 变化时没有正确触发 input 事件
- 缺少 watch 监听 selectedAddress 的变化

### 2. 任务类型二级联动问题

- 二级类型数据加载后没有正确设置到组件状态
- API 响应数据结构可能不正确
- 二级类型选择器的显示条件和数据绑定有问题

## 修复方案

### 1. 修复 AddressSelector 组件双向绑定

**文件**: `fuguang-uniapp/components/AddressSelector/AddressSelector.vue`

```javascript
// 添加watch监听selectedAddress变化
watch: {
  value(newVal) {
    this.selectedAddress = newVal
  },
  selectedAddress(newVal) {
    this.$emit('input', newVal)  // 确保v-model正常工作
  }
},

// 优化emitChange方法
emitChange(data) {
  console.log('地址选择变更:', data)
  this.selectedAddress = data.address
  this.$emit('input', data.address)
  this.$emit('change', data)
  console.log('地址已更新为:', this.selectedAddress)
}
```

### 2. 优化任务类型二级联动

**文件**: `fuguang-uniapp/pages/task/publish.vue`

```javascript
// 添加二级类型点击处理
onSecondTypeClick() {
  console.log('点击二级类型选择')
  console.log('当前二级类型数量:', this.secondTypes.length)

  if (this.secondTypes.length === 0) {
    uni.showToast({
      title: '请先选择一级类型',
      icon: 'none'
    })
    return
  }

  this.showSecondTypePicker = true
},

// 优化一级类型选择处理
async onFirstTypeConfirm(value) {
  // 添加详细的调试日志
  console.log('选择一级类型:', value)
  console.log('可选的一级类型:', this.firstTypes)

  const selectedType = this.firstTypes.find(item => item.typeId === value[0])

  if (selectedType) {
    // 设置一级类型
    this.form.firstTypeId = selectedType.typeId
    this.selectedFirstTypeName = selectedType.typeName

    // 重置并加载二级类型
    this.form.secondTypeId = ''
    this.selectedSecondTypeName = ''
    this.secondTypes = []

    // 加载二级类型数据
    const res = await getChildrenTaskTypes(selectedType.typeId)
    if (res && res.data) {
      this.secondTypes = res.data
      console.log('设置的二级类型数据:', this.secondTypes)
    }
  }
}
```

### 3. 添加调试功能

**文件**: `fuguang-uniapp/pages/test/address-task-type.vue`

- 添加测试按钮用于验证地址显示
- 添加清空地址功能
- 增强调试信息输出

## 测试步骤

### 1. 测试地址选择功能

1. 打开测试页面 `/pages/test/address-task-type`
2. 点击地址选择框
3. 选择一个地址选项
4. 检查地址是否正确回显在输入框中
5. 点击"测试地址显示"按钮查看详细信息

### 2. 测试任务类型功能

1. 在测试页面中点击"一级类型"
2. 选择一个一级类型
3. 检查是否显示"二级类型"选项
4. 点击"二级类型"
5. 检查是否能正常选择二级类型
6. 验证选择结果是否正确显示

### 3. 测试任务发布页面

1. 打开任务发布页面 `/pages/task/publish`
2. 测试地址选择功能
3. 测试任务类型选择功能
4. 检查表单数据是否正确

## 调试信息

### 控制台日志

- 地址选择: `地址选择变更:`, `地址已更新为:`
- 任务类型: `选择一级类型:`, `设置的二级类型数据:`
- API 调用: `二级任务类型API响应:`

### 常见问题排查

1. **地址不回显**

   - 检查控制台是否有`地址选择变更`日志
   - 确认 AddressSelector 组件的 v-model 绑定
   - 检查 selectedAddress 的值变化

2. **二级类型无法选择**

   - 检查一级类型是否正确选择
   - 查看`二级类型数据`日志输出
   - 确认 API 返回的数据格式

3. **表单数据不正确**
   - 检查 onAddressChange 方法是否被调用
   - 确认任务类型 ID 是否正确设置
   - 查看表单提交时的数据结构

## 后续优化建议

1. **错误处理**: 完善网络请求失败的处理机制
2. **用户体验**: 添加加载状态和过渡动画
3. **数据缓存**: 考虑缓存任务类型数据减少重复请求
4. **表单验证**: 增强表单验证逻辑和错误提示
